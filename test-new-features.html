<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新功能测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .feature-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        
        .feature-title {
            color: #007bff;
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        
        .test-section {
            background: #fff3cd;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            border-left: 4px solid #ffc107;
        }
        
        .test-title {
            color: #856404;
            font-size: 1.1em;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 新功能测试页面</h1>
            <p>测试最新添加的功能改进</p>
        </div>

        <div class="feature-section">
            <div class="feature-title">🆕 新增功能列表</div>
            <ul class="feature-list">
                <li>白名单IP列表添加删除按钮</li>
                <li>添加域名限制环境变量 (ALLOWED_DOMAINS)</li>
                <li>添加IP数量限制环境变量 (MAX_WHITELIST_IPS)</li>
                <li>支持通过订阅地址查询代理</li>
                <li>避免重复订阅地址的KV冗余</li>
                <li>管理界面支持代理ID或订阅地址输入</li>
            </ul>
        </div>

        <div class="feature-section">
            <div class="feature-title">🔧 环境变量配置</div>
            <p>在 <code>wrangler.toml</code> 中新增的环境变量：</p>
            <div class="code-block">
[vars]
# 允许进行代理的订阅地址域名，多个域名用逗号分隔，留空表示不限制
ALLOWED_DOMAINS = ""

# 每个代理ID的白名单IP上限数，留空表示不限制
MAX_WHITELIST_IPS = ""
            </div>
        </div>

        <div class="feature-section">
            <div class="feature-title">🗑️ 删除IP功能</div>
            <p>在管理界面的白名单IP列表中，每个IP旁边现在都有删除按钮：</p>
            <ul class="feature-list">
                <li>点击删除按钮会弹出确认对话框</li>
                <li>删除成功后自动刷新列表</li>
                <li>删除失败会显示错误信息</li>
            </ul>
        </div>

        <div class="feature-section">
            <div class="feature-title">🔍 订阅地址查询</div>
            <p>管理界面现在支持两种查询方式：</p>
            <ul class="feature-list">
                <li>使用代理ID查询（原有功能）</li>
                <li>使用完整的订阅地址查询（新功能）</li>
                <li>系统会自动判断输入类型并进行相应处理</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">🧪 测试步骤</div>
            <ol>
                <li><strong>测试域名限制：</strong>
                    <ul>
                        <li>在 wrangler.toml 中设置 ALLOWED_DOMAINS = "example.com"</li>
                        <li>尝试创建非允许域名的代理，应该被拒绝</li>
                    </ul>
                </li>
                <li><strong>测试IP数量限制：</strong>
                    <ul>
                        <li>在 wrangler.toml 中设置 MAX_WHITELIST_IPS = "3"</li>
                        <li>尝试添加超过3个IP，应该被拒绝</li>
                    </ul>
                </li>
                <li><strong>测试删除IP功能：</strong>
                    <ul>
                        <li>创建代理并添加几个IP</li>
                        <li>在管理界面查看IP列表</li>
                        <li>点击删除按钮测试删除功能</li>
                    </ul>
                </li>
                <li><strong>测试订阅地址查询：</strong>
                    <ul>
                        <li>创建代理后记录订阅地址</li>
                        <li>在管理界面使用订阅地址登录</li>
                        <li>验证能否正常管理</li>
                    </ul>
                </li>
                <li><strong>测试重复订阅地址：</strong>
                    <ul>
                        <li>尝试创建相同订阅地址的代理</li>
                        <li>应该提示已存在并显示现有代理ID</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="/" class="btn">🏠 返回首页</a>
            <a href="/whitelist-helper" class="btn">🛠️ 白名单助手</a>
        </div>
    </div>
</body>
</html>
